{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/ReactJs/Arablms/frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/ReactJs/Arablms/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/ReactJs/Arablms/frontend/src/app/page.tsx"], "sourcesContent": ["import { Button } from \"@/components/ui/button\";\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\">\n      <div className=\"container mx-auto px-4 py-16\">\n        <div className=\"text-center space-y-8\">\n          {/* Header */}\n          <div className=\"space-y-4\">\n            <h1 className=\"text-4xl md:text-6xl font-bold text-gray-900 dark:text-white\">\n              مرحباً بكم في{\" \"}\n              <span className=\"text-blue-600 dark:text-blue-400\">ArabLMS</span>\n            </h1>\n            <p className=\"text-xl md:text-2xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto\">\n              منصة تعليمية متطورة تمكن المدرسين من إنشاء مدارس رقمية مخصصة وتقديم الدورات للطلاب\n            </p>\n          </div>\n\n          {/* Features */}\n          <div className=\"grid md:grid-cols-3 gap-8 mt-16\">\n            <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\">\n              <div className=\"text-3xl mb-4\">🎓</div>\n              <h3 className=\"text-xl font-semibold mb-2 text-gray-900 dark:text-white\">للمدرسين</h3>\n              <p className=\"text-gray-600 dark:text-gray-300\">\n                إنشاء مدرسة رقمية بعلامة تجارية مخصصة وتقديم الدورات بسهولة\n              </p>\n            </div>\n\n            <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\">\n              <div className=\"text-3xl mb-4\">📚</div>\n              <h3 className=\"text-xl font-semibold mb-2 text-gray-900 dark:text-white\">للطلاب</h3>\n              <p className=\"text-gray-600 dark:text-gray-300\">\n                اكتشاف وشراء واستهلاك الدورات التعليمية بسهولة ويسر\n              </p>\n            </div>\n\n            <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\">\n              <div className=\"text-3xl mb-4\">💰</div>\n              <h3 className=\"text-xl font-semibold mb-2 text-gray-900 dark:text-white\">للمنصة</h3>\n              <p className=\"text-gray-600 dark:text-gray-300\">\n                إيرادات مستدامة من خلال رسوم المعاملات\n              </p>\n            </div>\n          </div>\n\n          {/* Tech Stack */}\n          <div className=\"mt-16 space-y-6\">\n            <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n              التقنيات المستخدمة\n            </h2>\n            <div className=\"flex flex-wrap justify-center gap-4\">\n              <span className=\"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm\">\n                Next.js 15+\n              </span>\n              <span className=\"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm\">\n                NestJS\n              </span>\n              <span className=\"bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-3 py-1 rounded-full text-sm\">\n                TypeScript\n              </span>\n              <span className=\"bg-cyan-100 dark:bg-cyan-900 text-cyan-800 dark:text-cyan-200 px-3 py-1 rounded-full text-sm\">\n                Tailwind CSS\n              </span>\n              <span className=\"bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 px-3 py-1 rounded-full text-sm\">\n                Shadcn UI\n              </span>\n            </div>\n          </div>\n\n          {/* CTA Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center mt-12\">\n            <Button size=\"lg\" className=\"text-lg px-8 py-3\">\n              ابدأ كمدرس\n            </Button>\n            <Button variant=\"outline\" size=\"lg\" className=\"text-lg px-8 py-3\">\n              تصفح الدورات\n            </Button>\n          </div>\n\n          {/* Status */}\n          <div className=\"mt-16 p-6 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800\">\n            <h3 className=\"text-lg font-semibold text-yellow-800 dark:text-yellow-200 mb-2\">\n              🚧 حالة المشروع\n            </h3>\n            <p className=\"text-yellow-700 dark:text-yellow-300\">\n              المشروع قيد التطوير - تم إعداد البنية الأساسية بنجاح!\n            </p>\n            <div className=\"mt-4 text-sm text-yellow-600 dark:text-yellow-400\">\n              <p>✅ إعداد Next.js 15+ مع App Router</p>\n              <p>✅ إعداد NestJS Backend</p>\n              <p>✅ تكوين TypeScript</p>\n              <p>✅ إعداد Tailwind CSS و Shadcn UI</p>\n              <p>✅ دعم RTL للغة العربية</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAA+D;oCAC7D;kDACd,8OAAC;wCAAK,WAAU;kDAAmC;;;;;;;;;;;;0CAErD,8OAAC;gCAAE,WAAU;0CAAyE;;;;;;;;;;;;kCAMxF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,8OAAC;wCAAG,WAAU;kDAA2D;;;;;;kDACzE,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;0CAKlD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,8OAAC;wCAAG,WAAU;kDAA2D;;;;;;kDACzE,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;0CAKlD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,8OAAC;wCAAG,WAAU;kDAA2D;;;;;;kDACzE,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;;;;;;;kCAOpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmD;;;;;;0CAGjE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAA+F;;;;;;kDAG/G,8OAAC;wCAAK,WAAU;kDAAmG;;;;;;kDAGnH,8OAAC;wCAAK,WAAU;kDAAuG;;;;;;kDAGvH,8OAAC;wCAAK,WAAU;kDAA+F;;;;;;kDAG/G,8OAAC;wCAAK,WAAU;kDAAuG;;;;;;;;;;;;;;;;;;kCAO3H,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8IAAA,CAAA,SAAM;gCAAC,MAAK;gCAAK,WAAU;0CAAoB;;;;;;0CAGhD,8OAAC,8IAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,WAAU;0CAAoB;;;;;;;;;;;;kCAMpE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAkE;;;;;;0CAGhF,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAGpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAE;;;;;;kDACH,8OAAC;kDAAE;;;;;;kDACH,8OAAC;kDAAE;;;;;;kDACH,8OAAC;kDAAE;;;;;;kDACH,8OAAC;kDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB", "debugId": null}}]}