[{"C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\frontend\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\frontend\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\frontend\\src\\components\\ui\\button.tsx": "3", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\frontend\\src\\lib\\utils.ts": "4"}, {"size": 689, "mtime": 1747062758839, "results": "5", "hashOfConfig": "6"}, {"size": 4925, "mtime": 1748196052748, "results": "7", "hashOfConfig": "6"}, {"size": 1835, "mtime": 1748196017678, "results": "8", "hashOfConfig": "6"}, {"size": 166, "mtime": 1748195939977, "results": "9", "hashOfConfig": "6"}, {"filePath": "10", "messages": "11", "suppressedMessages": "12", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "192sbib", {"filePath": "13", "messages": "14", "suppressedMessages": "15", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\frontend\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\frontend\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\frontend\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\frontend\\src\\lib\\utils.ts", [], []]